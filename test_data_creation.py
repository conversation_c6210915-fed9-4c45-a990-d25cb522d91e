#!/usr/bin/env python3
"""
Test script to run the data creation process on a few order_ids
"""

import asyncio
import os
from create_test_data import TestDataCreator

async def test_data_creation():
    """Test the data creation process with a few orders"""
    
    # Configuration
    csv_path = "/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv"
    pdf_output_dir = "./test_input_pdfs"
    json_output_dir = "./test_true_data"
    max_orders = 4  # Test with 4 orders
    
    print(f"Testing data creation with {max_orders} orders...")
    print(f"CSV file: {csv_path}")
    print(f"PDF output directory: {pdf_output_dir}")
    print(f"JSON output directory: {json_output_dir}")
    
    # Create the test data creator
    creator = TestDataCreator()
    
    # Process the orders
    await creator.process_orders(
        csv_path=csv_path,
        pdf_output_dir=pdf_output_dir,
        json_output_dir=json_output_dir,
        max_orders=max_orders
    )
    
    print("\nTest completed!")
    print(f"Check {pdf_output_dir} for merged PDF files")
    print(f"Check {json_output_dir} for true data JSON files")

if __name__ == "__main__":
    asyncio.run(test_data_creation())

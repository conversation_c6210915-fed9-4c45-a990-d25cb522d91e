#!/usr/bin/env python3
"""
Test script to run the data creation process on a few order_ids
"""

import asyncio
import os
from create_test_data import TestDataCreator

async def test_data_creation():
    """Test the data creation process with a few orders"""

    # Configuration
    csv_path = "/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv"
    pdf_output_dir = "./test_input_pdfs_filtered"
    json_output_dir = "./test_true_data_filtered"
    max_orders = 3  # Test with 3 orders
    allowed_types = ["invoice", "bol", "pod"]  # Filter only these types

    print(f"Testing data creation with {max_orders} orders...")
    print(f"CSV file: {csv_path}")
    print(f"PDF output directory: {pdf_output_dir}")
    print(f"JSON output directory: {json_output_dir}")
    print(f"Allowed attachment types: {allowed_types}")

    # Create the test data creator with filtering
    creator = TestDataCreator(allowed_attachment_types=allowed_types)

    # Process the orders
    await creator.process_orders(
        csv_path=csv_path,
        pdf_output_dir=pdf_output_dir,
        json_output_dir=json_output_dir,
        max_orders=max_orders
    )

    print("\nTest completed!")
    print(f"Check {pdf_output_dir} for merged PDF files and individual order folders")
    print(f"Check {json_output_dir} for true data JSON files")
    print(f"Check {pdf_output_dir}/file_tracking.csv for detailed metadata")

if __name__ == "__main__":
    asyncio.run(test_data_creation())

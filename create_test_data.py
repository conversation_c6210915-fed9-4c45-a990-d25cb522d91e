#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create test data from CSV file:
1. Downloads PDFs from S3 and merges them by order_id (filtered by attachment types)
2. Creates true data JSON files with document types and page numbers
3. Creates file tracking CSV for metadata
4. Organizes files in order_id folders with individual PDFs
"""

import asyncio
import argparse
import csv
import json
import os
from pathlib import Path
from typing import Dict, List, Tuple, Set, Optional
import boto3
from PyPDF2 import PdfMerger, PdfReader
import tempfile
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestDataCreator:
    def __init__(self, bucket_name: str = "tms-upload-prod",
                 allowed_attachment_types: List[str] = None):
        self.bucket_name = bucket_name
        self.s3_client = boto3.client('s3')
        self.allowed_attachment_types = set(allowed_attachment_types or ["invoice", "bol", "pod"])
        self.file_tracking_data = []
    
    async def download_pdf_from_s3(self, key: str, local_path: str) -> Tuple[bool, int]:
        """Download PDF from S3 to local path and return success status and page count"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self.s3_client.download_file,
                self.bucket_name,
                key,
                local_path
            )

            # Count pages in the downloaded PDF
            try:
                reader = PdfReader(local_path)
                page_count = len(reader.pages)
            except Exception as e:
                logger.warning(f"Could not count pages in {local_path}: {e}")
                page_count = 1  # Default to 1 page if we can't read it

            logger.info(f"Downloaded {key} to {local_path} ({page_count} pages)")
            return True, page_count
        except Exception as e:
            logger.error(f"Failed to download {key}: {str(e)}")
            return False, 0
    
    def merge_pdfs(self, pdf_paths: List[str], output_path: str) -> bool:
        """Merge multiple PDFs into one"""
        try:
            merger = PdfMerger()
            for pdf_path in pdf_paths:
                if os.path.exists(pdf_path):
                    merger.append(pdf_path)
                else:
                    logger.warning(f"PDF file not found: {pdf_path}")
            
            with open(output_path, 'wb') as output_file:
                merger.write(output_file)
            merger.close()
            logger.info(f"Merged {len(pdf_paths)} PDFs into {output_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to merge PDFs: {str(e)}")
            return False
    
    def create_true_data_json(self, documents: List[Dict], output_path: str) -> bool:
        """Create true data JSON file with proper page numbering"""
        try:
            true_data = {"documents": documents}
            with open(output_path, 'w') as f:
                json.dump(true_data, f, indent=4)
            logger.info(f"Created true data JSON: {output_path} with {len(documents)} document entries")
            return True
        except Exception as e:
            logger.error(f"Failed to create true data JSON: {str(e)}")
            return False

    def create_file_tracking_csv(self, output_path: str) -> bool:
        """Create file tracking CSV with all metadata"""
        try:
            if not self.file_tracking_data:
                logger.warning("No file tracking data to write")
                return True

            fieldnames = self.file_tracking_data[0].keys()
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.file_tracking_data)

            logger.info(f"Created file tracking CSV: {output_path} with {len(self.file_tracking_data)} records")
            return True
        except Exception as e:
            logger.error(f"Failed to create file tracking CSV: {str(e)}")
            return False
    
    async def process_order(self, order_id: str, order_data: List[Dict],
                          pdf_output_dir: str, json_output_dir: str) -> bool:
        """Process a single order: download PDFs, merge them, and create true data JSON"""
        logger.info(f"Processing order: {order_id}")

        # Create order-specific folder for individual PDFs
        order_folder = os.path.join(pdf_output_dir, order_id)
        os.makedirs(order_folder, exist_ok=True)

        # Create temporary directory for downloaded PDFs
        with tempfile.TemporaryDirectory() as temp_dir:
            downloaded_pdfs = []
            documents = []
            page_counter = 1
            sequence_number = 1  # For maintaining original CSV order in file naming

            # Process all documents in original CSV order
            for doc in order_data:
                attachment_type = doc['attachment_type']

                # Check if this attachment type should be processed
                if attachment_type not in self.allowed_attachment_types:
                    # Record skipped document
                    tracking_record = {
                        **doc,
                        'processed_timestamp': datetime.now().isoformat(),
                        'download_success': False,
                        'individual_file_path': '',
                        'page_count': 0,
                        'included_in_merge': False,
                        'filter_reason': f'attachment_type_not_allowed'
                    }
                    self.file_tracking_data.append(tracking_record)
                    continue  # Skip to next document

                # Process allowed attachment type
                s3_key = doc['path_to_file']

                # Extract filename from S3 key
                s3_filename = os.path.basename(s3_key)

                # Create individual file name: [srno]_[attachment_type]_[filename]
                individual_filename = f"{sequence_number}_{attachment_type}_{s3_filename}"
                individual_pdf_path = os.path.join(order_folder, individual_filename)

                # Temporary path for merging
                temp_pdf_path = os.path.join(temp_dir, f"{order_id}_{sequence_number}.pdf")

                success, page_count = await self.download_pdf_from_s3(s3_key, temp_pdf_path)
                if success:
                    # Copy to individual file location
                    import shutil
                    shutil.copy2(temp_pdf_path, individual_pdf_path)

                    downloaded_pdfs.append(temp_pdf_path)

                    # Add to documents list for true data (considering multiple pages)
                    for page_num in range(page_count):
                        documents.append({
                            "page_no": page_counter + page_num,
                            "doc_type": attachment_type
                        })
                    page_counter += page_count

                    # Record file tracking data
                    tracking_record = {
                        **doc,  # Include all original CSV data
                        'processed_timestamp': datetime.now().isoformat(),
                        'download_success': True,
                        'individual_file_path': individual_pdf_path,
                        'page_count': page_count,
                        'included_in_merge': True,
                        'filter_reason': 'included'
                    }
                else:
                    # Record failed download
                    tracking_record = {
                        **doc,
                        'processed_timestamp': datetime.now().isoformat(),
                        'download_success': False,
                        'individual_file_path': '',
                        'page_count': 0,
                        'included_in_merge': False,
                        'filter_reason': 'download_failed'
                    }

                self.file_tracking_data.append(tracking_record)
                sequence_number += 1  # Increment for next document

            if not downloaded_pdfs:
                logger.error(f"No PDFs downloaded for order {order_id}")
                return False

            # Merge PDFs (outside the order folder)
            merged_pdf_path = os.path.join(pdf_output_dir, f"{order_id}.pdf")
            merge_success = self.merge_pdfs(downloaded_pdfs, merged_pdf_path)

            # Create true data JSON
            json_path = os.path.join(json_output_dir, f"{order_id}.json")
            json_success = self.create_true_data_json(documents, json_path)

            return merge_success and json_success
    
    def read_csv_data(self, csv_path: str) -> Dict[str, List[Dict]]:
        """Read CSV and group data by order_id"""
        orders_data = {}
        
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                order_id = row['order_id']
                if order_id not in orders_data:
                    orders_data[order_id] = []
                orders_data[order_id].append(row)
        
        logger.info(f"Found {len(orders_data)} unique orders in CSV")
        return orders_data
    
    async def process_orders(self, csv_path: str, pdf_output_dir: str,
                           json_output_dir: str, max_orders: Optional[int] = None) -> None:
        """Process multiple orders concurrently"""
        # Create output directories
        os.makedirs(pdf_output_dir, exist_ok=True)
        os.makedirs(json_output_dir, exist_ok=True)

        # Read CSV data
        orders_data = self.read_csv_data(csv_path)

        # Limit number of orders if specified
        if max_orders:
            order_ids = list(orders_data.keys())[:max_orders]
            orders_data = {oid: orders_data[oid] for oid in order_ids}
            logger.info(f"Processing first {max_orders} orders")

        logger.info(f"Allowed attachment types: {sorted(self.allowed_attachment_types)}")

        # Process orders concurrently
        semaphore = asyncio.Semaphore(5)  # Limit concurrent downloads

        async def process_with_semaphore(order_id: str, order_data: List[Dict]):
            async with semaphore:
                return await self.process_order(order_id, order_data, pdf_output_dir, json_output_dir)

        tasks = [
            process_with_semaphore(order_id, order_data)
            for order_id, order_data in orders_data.items()
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Create file tracking CSV
        tracking_csv_path = os.path.join(pdf_output_dir, "file_tracking.csv")
        self.create_file_tracking_csv(tracking_csv_path)

        # Report results
        successful = sum(1 for r in results if r is True)
        failed = len(results) - successful
        logger.info(f"Processing complete: {successful} successful, {failed} failed")
        logger.info(f"File tracking CSV created: {tracking_csv_path}")

async def main():
    parser = argparse.ArgumentParser(description='Create test data from CSV file')
    parser.add_argument('csv_path', help='Path to input CSV file')
    parser.add_argument('pdf_output_dir', help='Directory to store merged PDF files')
    parser.add_argument('json_output_dir', help='Directory to store true data JSON files')
    parser.add_argument('--max-orders', type=int, default=None,
                       help='Maximum number of orders to process (default: all)')
    parser.add_argument('--allowed-types', nargs='+',
                       default=['invoice', 'bol', 'pod'],
                       help='Allowed attachment types to process (default: invoice bol pod)')

    args = parser.parse_args()

    creator = TestDataCreator(allowed_attachment_types=args.allowed_types)
    await creator.process_orders(
        args.csv_path,
        args.pdf_output_dir,
        args.json_output_dir,
        args.max_orders
    )

if __name__ == "__main__":
    asyncio.run(main())

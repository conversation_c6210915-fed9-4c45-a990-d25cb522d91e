#!/bin/bash

# <PERSON>ript to run test data creation with command line arguments
# Usage: ./run_test_data_creation.sh <pdf_output_dir> <json_output_dir> [max_orders]

set -e

# Default values
CSV_PATH="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv"
MAX_ORDERS=${3:-4}  # Default to 4 orders if not specified

# Check arguments
if [ $# -lt 2 ]; then
    echo "Usage: $0 <pdf_output_dir> <json_output_dir> [max_orders]"
    echo "Example: $0 ./input_pdfs ./true_data 5"
    exit 1
fi

PDF_OUTPUT_DIR=$1
JSON_OUTPUT_DIR=$2

echo "Creating test data..."
echo "CSV file: $CSV_PATH"
echo "PDF output directory: $PDF_OUTPUT_DIR"
echo "JSON output directory: $JSON_OUTPUT_DIR"
echo "Max orders to process: $MAX_ORDERS"
echo ""

# Run the Python script
python create_test_data.py "$CSV_PATH" "$PDF_OUTPUT_DIR" "$JSON_OUTPUT_DIR" --max-orders "$MAX_ORDERS"

echo ""
echo "Test data creation completed!"
echo "Merged PDFs are in: $PDF_OUTPUT_DIR"
echo "True data JSON files are in: $JSON_OUTPUT_DIR"

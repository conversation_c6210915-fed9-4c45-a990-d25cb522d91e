#!/usr/bin/env python3
"""
Example usage of the create_test_data function
"""

import asyncio
from create_test_data import create_test_data

async def main():
    """Example usage of the create_test_data function"""
    
    # Example 1: Basic usage with default settings
    await create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./example_output",
        json_output_dir="./example_json",
        max_orders=2
    )
    
    # Example 2: Custom attachment types
    await create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./custom_output",
        json_output_dir="./custom_json",
        max_orders=3,
        allowed_types=["invoice", "bol", "pod", "combined_carrier_documents"]
    )
    
    # Example 3: Process all orders with specific types
    await create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./all_invoices",
        json_output_dir="./all_invoices_json",
        max_orders=None,  # Process all orders
        allowed_types=["invoice"]  # Only invoices
    )

if __name__ == "__main__":
    asyncio.run(main())

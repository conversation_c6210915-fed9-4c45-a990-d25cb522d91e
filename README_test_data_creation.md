# Test Data Creation Scripts

This repository contains scripts to create test data from a CSV file containing order information and S3 file paths.

## Overview

The scripts will:
1. **Create merged PDFs**: Download PDFs from S3 for each order_id and merge them into a single PDF file
2. **Create true data JSON files**: Generate JSON files with document type and page number information

## Files

- `create_test_data.py` - Main Python script for processing orders
- `test_data_creation.py` - Simple test script that processes 4 orders
- `run_test_data_creation.sh` - Bash script wrapper for easy command-line usage
- `requirements_test_data.txt` - Python dependencies

## Prerequisites

1. **Python Dependencies**: Install required packages
   ```bash
   pip install -r requirements_test_data.txt
   ```

2. **AWS Credentials**: Ensure AWS credentials are configured for S3 access
   ```bash
   aws configure
   # or ensure ~/.aws/credentials is properly set up
   ```

3. **S3 Access**: The script accesses the `tms-upload-prod` S3 bucket

## Usage

### Method 1: Using the bash script (Recommended)
```bash
./run_test_data_creation.sh <pdf_output_dir> <json_output_dir> [max_orders]
```

**Examples:**
```bash
# Process 4 orders (default)
./run_test_data_creation.sh ./input_pdfs ./true_data

# Process 10 orders
./run_test_data_creation.sh ./input_pdfs ./true_data 10

# Process all orders (warning: this could be thousands!)
./run_test_data_creation.sh ./input_pdfs ./true_data
```

### Method 2: Using Python directly
```bash
python create_test_data.py <csv_path> <pdf_output_dir> <json_output_dir> --max-orders <number>
```

**Example:**
```bash
python create_test_data.py \
  "/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv" \
  "./input_pdfs" \
  "./true_data" \
  --max-orders 5
```

### Method 3: Quick test with predefined settings
```bash
python test_data_creation.py
```

## Output Structure

### Merged PDFs
- **Location**: Specified PDF output directory
- **Naming**: `{order_id}.pdf` (e.g., `11630185.pdf`)
- **Content**: All PDFs for an order merged in the sequence they appear in the CSV file

### True Data JSON Files
- **Location**: Specified JSON output directory  
- **Naming**: `{order_id}.json` (e.g., `11630185.json`)
- **Structure**:
  ```json
  {
      "documents": [
          {
              "page_no": 1,
              "doc_type": "bol"
          },
          {
              "page_no": 2,
              "doc_type": "invoice"
          }
      ]
  }
  ```

## CSV File Format

The input CSV file should have these columns:
- `order_id` - Unique identifier for each order
- `attachment_type` - Document type (bol, invoice, pod, etc.)
- `path_to_file` - S3 key path to the PDF file

## Features

- **Async Processing**: Downloads and processes multiple orders concurrently
- **Error Handling**: Continues processing even if some files fail to download
- **Progress Logging**: Detailed logging of download and merge operations
- **Configurable Concurrency**: Limited to 5 concurrent downloads to avoid overwhelming S3

## Example Test Run

The test run processed 4 orders successfully:
- `11630185.pdf` (2.3M) - 4 documents
- `11629776.pdf` (394K) - 4 documents  
- `11626591.pdf` (1.3M) - 7 documents
- `11626726.pdf` (1.8M) - 11 documents

## Troubleshooting

1. **AWS Credentials Error**: Ensure AWS credentials are properly configured
2. **S3 Access Denied**: Verify you have read access to the `tms-upload-prod` bucket
3. **File Not Found**: Some S3 keys in the CSV might not exist - the script will log errors and continue
4. **Memory Issues**: For large numbers of orders, consider processing in smaller batches
